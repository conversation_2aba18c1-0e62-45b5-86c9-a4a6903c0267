/**
 * Session Timer Bar Component
 * 
 * Enhanced timer bar that supports session-based timing with multiple
 * timer instances, session selection, and improved visual hierarchy.
 */

import React, { useState, memo, useCallback } from 'react';
import {
  Box,
  Button,
  Typography,
  Paper,
  Autocomplete,
  TextField,
  Chip,
  Stack,
  Divider,
  IconButton,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
} from '@mui/material';
import {
  PlayArrow as PlayIcon,
  Stop as StopIcon,
  Pause as PauseIcon,
  Notes as NotesIcon,
  NoteAdd as NoteAddIcon,
  Add as AddIcon,
  MoreVert as MoreIcon,
  FolderOpen as SessionIcon,
  RadioButtonChecked as RunningIcon,
  PauseCircle as PausedIcon,
  StopCircle as StoppedIcon,
} from '@mui/icons-material';
import { TaskSession, TimerInstance } from '../../types/timer';
import { Task } from '../../types/task';
import { useTimer } from '../../hooks/useTimer';
import { formatTime } from '../../utils/formatters';

// Status indicator component with animations
interface TimerStatusIndicatorProps {
  status: 'running' | 'paused' | 'stopped';
  size?: 'small' | 'medium';
}

function TimerStatusIndicator({ status, size = 'medium' }: TimerStatusIndicatorProps) {
  const getStatusConfig = () => {
    switch (status) {
      case 'running':
        return {
          color: 'success.main',
          icon: <RunningIcon />,
          label: 'Running',
          animation: 'pulse 2s infinite'
        };
      case 'paused':
        return {
          color: 'warning.main',
          icon: <PausedIcon />,
          label: 'Paused',
          animation: 'none'
        };
      case 'stopped':
        return {
          color: 'grey.500',
          icon: <StoppedIcon />,
          label: 'Stopped',
          animation: 'none'
        };
    }
  };

  const config = getStatusConfig();
  const iconSize = size === 'small' ? 16 : 20;

  return (
    <Box
      sx={{
        display: 'flex',
        alignItems: 'center',
        gap: 0.5,
        '@keyframes pulse': {
          '0%': { opacity: 1 },
          '50%': { opacity: 0.6 },
          '100%': { opacity: 1 },
        },
      }}
    >
      <Box
        sx={{
          color: config.color,
          animation: config.animation,
          display: 'flex',
          alignItems: 'center',
          '& svg': { fontSize: iconSize },
        }}
      >
        {config.icon}
      </Box>
      {size === 'medium' && (
        <Typography variant="caption" sx={{ color: config.color, fontWeight: 600 }}>
          {config.label}
        </Typography>
      )}
    </Box>
  );
}

interface SessionTimerBarProps {
  activeSession: TaskSession | null;
  predefinedTasks: Task[];
  onStartSession: (taskId: string, taskName: string) => Promise<TaskSession>;
  onStopSession: (sessionId: string) => Promise<void>;
  onCreateTimerInstance: (sessionId: string) => Promise<TimerInstance>;
  onStartTimer: (instanceId: string) => Promise<void>;
  onStopTimer: (instanceId: string) => Promise<void>;
  onPauseTimer: (instanceId: string) => Promise<void>;
  onResumeTimer: (instanceId: string) => Promise<void>;
  onOpenSessionNotes?: (sessionId: string) => void;
  onOpenTimerNotes?: (instanceId: string) => void;
}

export function SessionTimerBar({
  activeSession,
  predefinedTasks,
  onStartSession,
  onStopSession,
  onCreateTimerInstance,
  onStartTimer,
  onStopTimer,
  onPauseTimer,
  onResumeTimer,
  onOpenSessionNotes,
  onOpenTimerNotes,
}: SessionTimerBarProps) {
  const [selectedTask, setSelectedTask] = useState<string>('');
  const [inputValue, setInputValue] = useState<string>('');
  const [menuAnchor, setMenuAnchor] = useState<null | HTMLElement>(null);

  // Get running timer instance
  const runningInstance = activeSession?.timerInstances?.find(i => i.isRunning);

  // Calculate elapsed time for running timer
  // Ensure startTime is properly converted to Date object
  const startTime = runningInstance?.startTime
    ? (runningInstance.startTime instanceof Date
        ? runningInstance.startTime
        : new Date(runningInstance.startTime))
    : undefined;

  const elapsed = useTimer(
    runningInstance?.isRunning || false,
    startTime
  );

  // Helper function to calculate elapsed time for any timer instance
  const getInstanceElapsedTime = (instance: TimerInstance): number => {
    if (instance.isRunning) {
      // For running timers, use the live elapsed time
      return elapsed;
    } else if (instance.isPaused) {
      // For paused timers, calculate time up to pause point
      const startTime = instance.startTime instanceof Date
        ? instance.startTime
        : new Date(instance.startTime);
      const pausedAt = instance.pausedAt instanceof Date
        ? instance.pausedAt
        : new Date(instance.pausedAt || Date.now());

      const elapsedToPause = pausedAt.getTime() - startTime.getTime();
      const previousPausedDuration = instance.pausedDuration || 0;
      return Math.max(0, elapsedToPause - previousPausedDuration);
    } else {
      // For stopped timers, use the final duration
      return instance.duration || 0;
    }
  };

  const taskOptions = predefinedTasks.map(task => ({ label: task.name, id: task.id }));

  const handleStartSession = async () => {
    // Get the task name from either selected dropdown option or manual input
    let taskName = '';
    let taskId = '';

    if (selectedTask) {
      // If a task was selected from dropdown, find the corresponding task object
      const selectedTaskObj = predefinedTasks.find(t => t.name === selectedTask);
      if (selectedTaskObj) {
        taskName = selectedTaskObj.name;
        taskId = selectedTaskObj.id;
      } else {
        // selectedTask might be a manually entered value
        taskName = selectedTask.trim();
        taskId = '';
      }
    } else if (inputValue) {
      // Manual input
      taskName = inputValue.trim();
      taskId = '';
    }

    // Validate task name before proceeding
    if (!taskName) {
      console.error('Cannot start session: task name is empty');
      return;
    }

    try {
      console.log('Starting session with:', { taskId, taskName }); // Debug log
      const newSession = await onStartSession(taskId, taskName);
      // Session now automatically creates and starts first timer instance
      console.log('Session started successfully with auto-timer:', newSession.id);
      setSelectedTask('');
      setInputValue('');
    } catch (error) {
      console.error('Failed to start session:', error);
    }
  };

  const handleStopSession = async () => {
    if (activeSession) {
      // Stop any running timers first
      if (runningInstance) {
        await onStopTimer(runningInstance.id);
      }
      await onStopSession(activeSession.id);
    }
  };

  const handleAddTimerInstance = useCallback(async () => {
    if (!activeSession) {
      console.warn('No active session available for adding timer instance');
      return;
    }

    try {
      console.log('Adding timer instance to session:', activeSession.id);
      const newInstance = await onCreateTimerInstance(activeSession.id);
      console.log('Timer instance created:', newInstance.id);

      await onStartTimer(newInstance.id);
      console.log('Timer instance started successfully:', newInstance.id);
    } catch (error) {
      console.error('Failed to add timer instance:', error);
      // Show user-friendly error message
      alert(`Failed to add timer: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }, [activeSession, onCreateTimerInstance, onStartTimer]);

  const handleMenuOpen = useCallback((event: React.MouseEvent<HTMLElement>) => {
    setMenuAnchor(event.currentTarget);
  }, []);

  const handleMenuClose = useCallback(() => {
    setMenuAnchor(null);
  }, []);

  // No active session - show session creation interface
  if (!activeSession) {
    return (
      <Paper 
        elevation={2} 
        sx={{ 
          p: 2, 
          bgcolor: 'background.paper',
          borderRadius: 2,
          border: '1px solid',
          borderColor: 'divider'
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <SessionIcon color="primary" />
          <Typography variant="h6" sx={{ fontWeight: 600, color: 'text.primary' }}>
            Start New Session
          </Typography>
          
          <Box sx={{ flex: 1, display: 'flex', alignItems: 'center', gap: 2 }}>
            <Autocomplete
              value={selectedTask}
              onChange={(_, newValue) => {
                if (typeof newValue === 'string') {
                  setSelectedTask(newValue);
                } else if (newValue?.label) {
                  // When selecting from dropdown, use the actual task name (which is the label)
                  setSelectedTask(newValue.label);
                } else {
                  setSelectedTask('');
                }
              }}
              inputValue={inputValue}
              onInputChange={(_, newInputValue) => setInputValue(newInputValue)}
              options={taskOptions}
              freeSolo
              sx={{ minWidth: 300, flex: 1 }}
              renderInput={(params) => (
                <TextField
                  {...params}
                  placeholder="Select or enter task name..."
                  variant="outlined"
                  size="small"
                />
              )}
            />
            
            <Button
              variant="contained"
              color="primary"
              startIcon={<PlayIcon />}
              onClick={handleStartSession}
              disabled={!selectedTask.trim() && !inputValue.trim()}
              sx={{ minWidth: 120 }}
            >
              Start Session
            </Button>
          </Box>
        </Box>
      </Paper>
    );
  }

  // Active session - show session controls and timer instances
  return (
    <Paper 
      elevation={2} 
      sx={{ 
        p: 2, 
        bgcolor: 'secondary.main',
        color: 'secondary.contrastText',
        borderRadius: 2,
      }}
    >
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
        <SessionIcon />
        <Typography variant="h6" sx={{ fontWeight: 600 }}>
          {activeSession.taskName}
        </Typography>
        
        <Chip
          label={`${activeSession.timerInstances?.length || 0} timer${(activeSession.timerInstances?.length || 0) !== 1 ? 's' : ''}`}
          size="small"
          sx={{
            bgcolor: 'secondary.dark',
            color: 'secondary.contrastText',
            fontWeight: 600
          }}
        />
        
        <Box sx={{ flex: 1 }} />
        
        {/* Session total time */}
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Typography variant="body2" sx={{ opacity: 0.8 }}>
            Total:
          </Typography>
          <Typography variant="h6" sx={{ fontFamily: 'monospace', fontWeight: 600 }}>
            {formatTime(
              (activeSession.timerInstances || []).reduce((total, instance) => {
                return total + getInstanceElapsedTime(instance);
              }, 0)
            )}
          </Typography>
        </Box>
        
        <IconButton
          onClick={handleMenuOpen}
          sx={{ color: 'secondary.contrastText' }}
        >
          <MoreIcon />
        </IconButton>
        
        <Button
          variant="outlined"
          color="error"
          startIcon={<StopIcon />}
          onClick={handleStopSession}
          sx={{ 
            color: 'secondary.contrastText', 
            borderColor: 'secondary.contrastText',
            '&:hover': {
              bgcolor: 'error.main',
              borderColor: 'error.main',
              color: 'error.contrastText'
            }
          }}
        >
          End Session
        </Button>
      </Box>

      <Divider sx={{ bgcolor: 'secondary.contrastText', opacity: 0.3, mb: 2 }} />

      {/* Timer instances */}
      <Stack direction="row" spacing={2} sx={{ alignItems: 'center' }}>
        {(activeSession.timerInstances || []).map((instance, index) => {
          const callbacks = {
            onStart: () => onStartTimer(instance.id),
            onStop: () => onStopTimer(instance.id),
            onPause: () => onPauseTimer(instance.id),
            onResume: () => onResumeTimer(instance.id),
            onOpenNotes: () => onOpenTimerNotes?.(instance.id),
          };

          return (
            <TimerInstanceCard
              key={instance.id}
              instance={instance}
              index={index + 1}
              isRunning={instance.isRunning}
              elapsed={getInstanceElapsedTime(instance)}
              {...callbacks}
            />
          );
        })}
        
        <Button
          variant="outlined"
          startIcon={<AddIcon />}
          onClick={handleAddTimerInstance}
          sx={{ 
            color: 'secondary.contrastText', 
            borderColor: 'secondary.contrastText',
            minWidth: 140
          }}
        >
          Add Timer
        </Button>
      </Stack>

      {/* Session menu */}
      <Menu
        anchorEl={menuAnchor}
        open={Boolean(menuAnchor)}
        onClose={handleMenuClose}
      >
        <MenuItem onClick={() => {
          onOpenSessionNotes?.(activeSession.id);
          handleMenuClose();
        }}>
          <ListItemIcon>
            <NotesIcon />
          </ListItemIcon>
          <ListItemText>Session Notes</ListItemText>
        </MenuItem>
      </Menu>
    </Paper>
  );
}

// Timer Instance Card Component
interface TimerInstanceCardProps {
  instance: TimerInstance;
  index: number;
  isRunning: boolean;
  elapsed: number;
  onStart: () => void;
  onStop: () => void;
  onPause: () => void;
  onResume: () => void;
  onOpenNotes: () => void;
}

const TimerInstanceCard = memo(function TimerInstanceCard({
  instance,
  index,
  isRunning,
  elapsed,
  onStart,
  onStop,
  onPause,
  onResume,
  onOpenNotes,
}: TimerInstanceCardProps) {
  return (
    <Box
      component="section"
      tabIndex={0}
      role="region"
      aria-label={`Timer ${index}: ${isRunning ? 'running' : instance.isPaused ? 'paused' : 'stopped'}`}
      sx={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        gap: 1,
        p: 1.5,
        borderRadius: 1,
        bgcolor: isRunning ? 'success.dark' : instance.isPaused ? 'warning.dark' : 'secondary.dark',
        color: 'white',
        minWidth: 120,
        transition: 'background-color 0.3s ease, transform 0.2s ease',
        '&:hover': {
          transform: 'translateY(-2px)',
          boxShadow: 2,
        },
        '&:focus': {
          outline: '2px solid white',
          outlineOffset: '2px',
        },
        ...(isRunning && {
          boxShadow: '0 0 10px rgba(76, 175, 80, 0.3)',
        }),
        ...(instance.isPaused && {
          boxShadow: '0 0 10px rgba(255, 152, 0, 0.3)',
        }),
      }}
      onKeyDown={(e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          if (isRunning) {
            onPause();
          } else if (instance.isPaused) {
            onResume();
          } else {
            onStart();
          }
        }
      }}
    >
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 0.5 }}>
        <Typography variant="caption" sx={{ fontWeight: 600, opacity: 0.8 }}>
          Timer {index}
        </Typography>
        <TimerStatusIndicator
          status={isRunning ? 'running' : instance.isPaused ? 'paused' : 'stopped'}
          size="small"
        />
      </Box>
      
      <Typography
        variant="body1"
        component="time"
        aria-label={`Timer ${index} elapsed time: ${formatTime(elapsed)}`}
        sx={{
          fontFamily: 'monospace',
          fontWeight: 600,
          fontSize: '0.9rem'
        }}
      >
        {formatTime(elapsed)}
      </Typography>
      
      <Box sx={{ display: 'flex', gap: 0.5 }} role="group" aria-label={`Timer ${index} controls`}>
        {isRunning ? (
          <>
            <IconButton
              size="small"
              onClick={onPause}
              sx={{ color: 'white' }}
              aria-label={`Pause timer ${index}`}
            >
              <PauseIcon fontSize="small" />
            </IconButton>
            <IconButton
              size="small"
              onClick={onStop}
              sx={{ color: 'white' }}
              aria-label={`Stop timer ${index}`}
            >
              <StopIcon fontSize="small" />
            </IconButton>
          </>
        ) : instance.isPaused ? (
          <>
            <IconButton
              size="small"
              onClick={onResume}
              sx={{ color: 'white' }}
              aria-label={`Resume timer ${index}`}
            >
              <PlayIcon fontSize="small" />
            </IconButton>
            <IconButton
              size="small"
              onClick={onStop}
              sx={{ color: 'white' }}
              aria-label={`Stop timer ${index}`}
            >
              <StopIcon fontSize="small" />
            </IconButton>
          </>
        ) : (
          <IconButton
            size="small"
            onClick={onStart}
            sx={{ color: 'white' }}
            aria-label={`Start timer ${index}`}
          >
            <PlayIcon fontSize="small" />
          </IconButton>
        )}

        <IconButton
          size="small"
          onClick={onOpenNotes}
          sx={{ color: 'white' }}
          aria-label={`Open notes for timer ${index}`}
        >
          <NoteAddIcon fontSize="small" />
        </IconButton>
      </Box>
    </Box>
  );
});