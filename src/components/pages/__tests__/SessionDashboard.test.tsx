/**
 * Unit Tests for SessionDashboard Component
 * 
 * Tests the main session management dashboard including session creation,
 * display, activation, and timer management functionality.
 */

import React from 'react';
import { render, screen, fireEvent, waitFor, act } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';
import { SessionDashboard } from '../SessionDashboard';
import { TaskSession, TimerInstance } from '../../../types/timer';
import { Task } from '../../../types/task';

// Mock dependencies
const mockInvoke = vi.fn();
vi.mock('@tauri-apps/api/tauri', () => ({
  invoke: mockInvoke,
}));

const mockShowError = vi.fn();
const mockShowSuccess = vi.fn();
const mockShowWarning = vi.fn();
vi.mock('../../../contexts/NotificationContext', () => ({
  useNotification: () => ({
    showError: mockShowError,
    showSuccess: mockShowSuccess,
    showWarning: mockShowWarning,
  }),
}));

// Mock session management hook
const mockCreateSession = vi.fn();
const mockUpdateSession = vi.fn();
const mockDeleteSession = vi.fn();
const mockCreateTimerInstance = vi.fn();
const mockStartTimer = vi.fn();
const mockStopTimer = vi.fn();
const mockPauseTimer = vi.fn();
const mockResumeTimer = vi.fn();

vi.mock('../../../hooks/useSessionManagement', () => ({
  useSessionManagement: () => ({
    sessions: [],
    activeSession: null,
    isLoading: false,
    error: null,
    createSession: mockCreateSession,
    updateSession: mockUpdateSession,
    deleteSession: mockDeleteSession,
    createTimerInstance: mockCreateTimerInstance,
    startTimer: mockStartTimer,
    stopTimer: mockStopTimer,
    pauseTimer: mockPauseTimer,
    resumeTimer: mockResumeTimer,
    refreshSessions: vi.fn(),
  }),
}));

// Mock task management
const mockTasks: Task[] = [
  {
    id: 'task-1',
    name: 'Development Work',
    description: 'Software development tasks',
    createdAt: '2024-01-15T09:00:00Z',
    updatedAt: '2024-01-15T09:00:00Z',
  },
  {
    id: 'task-2',
    name: 'Code Review',
    description: 'Review pull requests',
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-15T10:00:00Z',
  },
];

vi.mock('../../../hooks/useTaskManagement', () => ({
  useTaskManagement: () => ({
    tasks: mockTasks,
    isLoading: false,
    error: null,
  }),
}));

// Mock child components to isolate SessionDashboard testing
vi.mock('../../layout/SessionTimerBar', () => ({
  SessionTimerBar: ({ onCreateTimerInstance, onStartTimer }: any) => (
    <div data-testid="session-timer-bar">
      <button
        onClick={() => {
          const mockInstance = { id: 'timer-2' };
          onCreateTimerInstance('session-1').then(() => onStartTimer('timer-2'));
        }}
      >
        Add Timer
      </button>
    </div>
  ),
}));

vi.mock('../../ui/sessions/SessionList', () => ({
  SessionList: ({ sessions }: { sessions: TaskSession[] }) => (
    <div data-testid="session-list">
      {sessions.map((session) => (
        <div key={session.id} data-testid="session-card" className={session.isActive ? 'active' : ''}>
          <span>{session.taskName}</span>
          <span>{new Date(session.totalDuration).toISOString().substr(11, 8)}</span>
        </div>
      ))}
    </div>
  ),
}));

vi.mock('../../ui/display/SessionTimerDisplay', () => ({
  SessionTimerDisplay: ({ onStartTimer, onStopTimer, onPauseTimer, onResumeTimer, onOpenNotes }: any) => (
    <div data-testid="session-timer-display">
      <button onClick={() => onStartTimer('timer-1')}>Start Timer</button>
      <button onClick={() => onStopTimer('timer-1')}>Stop Timer</button>
      <button onClick={() => onPauseTimer('timer-1')}>Pause Timer</button>
      <button onClick={() => onResumeTimer('timer-1')}>Resume Timer</button>
      <button onClick={() => onOpenNotes('session-1', 'timer-1')}>Notes</button>
    </div>
  ),
}));

vi.mock('../../ui/dialogs/InactivityWarningDialog', () => ({
  InactivityWarningDialog: ({ onContinue, onPause }: any) => (
    <div data-testid="inactivity-warning">
      <span>Inactivity Warning</span>
      <button onClick={onContinue}>Continue Working</button>
      <button onClick={onPause}>Pause Timer</button>
    </div>
  ),
}));

vi.mock('../../ui/dialogs/EnhancedNotesDialog', () => ({
  EnhancedNotesDialog: ({ open, onClose }: any) =>
    open ? (
      <div data-testid="notes-dialog">
        <span>Notes Dialog</span>
        <button onClick={onClose}>Close</button>
      </div>
    ) : null,
}));

describe('SessionDashboard', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.clearAllTimers();
  });

  describe('Initial Rendering', () => {
    it('should render the dashboard with no sessions', () => {
      render(<SessionDashboard />);
      
      expect(screen.getByText('Sessions')).toBeInTheDocument();
      expect(screen.getByText('Start New Session')).toBeInTheDocument();
      expect(screen.getByPlaceholderText('Select or enter task name...')).toBeInTheDocument();
    });

    it('should show loading state when sessions are loading', () => {
      vi.mocked(require('../../../hooks/useSessionManagement').useSessionManagement).mockReturnValue({
        sessions: [],
        activeSession: null,
        isLoading: true,
        error: null,
        createSession: mockCreateSession,
        updateSession: mockUpdateSession,
        deleteSession: mockDeleteSession,
        createTimerInstance: mockCreateTimerInstance,
        startTimer: mockStartTimer,
        stopTimer: mockStopTimer,
        pauseTimer: mockPauseTimer,
        resumeTimer: mockResumeTimer,
        refreshSessions: vi.fn(),
      });

      render(<SessionDashboard />);
      
      expect(screen.getByRole('progressbar')).toBeInTheDocument();
    });

    it('should show error state when there is an error', () => {
      const errorMessage = 'Failed to load sessions';
      vi.mocked(require('../../../hooks/useSessionManagement').useSessionManagement).mockReturnValue({
        sessions: [],
        activeSession: null,
        isLoading: false,
        error: new Error(errorMessage),
        createSession: mockCreateSession,
        updateSession: mockUpdateSession,
        deleteSession: mockDeleteSession,
        createTimerInstance: mockCreateTimerInstance,
        startTimer: mockStartTimer,
        stopTimer: mockStopTimer,
        pauseTimer: mockPauseTimer,
        resumeTimer: mockResumeTimer,
        refreshSessions: vi.fn(),
      });

      render(<SessionDashboard />);
      
      expect(screen.getByText(errorMessage)).toBeInTheDocument();
    });
  });

  describe('Session Creation', () => {
    it('should create a new session when task is selected and start button clicked', async () => {
      const newSession: TaskSession = {
        id: 'session-1',
        taskId: 'task-1',
        taskName: 'Development Work',
        timerInstances: [],
        totalDuration: 0,
        isActive: true,
        date: '2024-01-15',
        createdAt: '2024-01-15T10:00:00Z',
        updatedAt: '2024-01-15T10:00:00Z',
      };

      mockCreateSession.mockResolvedValue(newSession);

      render(<SessionDashboard />);
      
      // Select a task
      const taskInput = screen.getByPlaceholderText('Select or enter task name...');
      fireEvent.change(taskInput, { target: { value: 'Development Work' } });
      
      // Click start button
      const startButton = screen.getByText('Start New Session');
      fireEvent.click(startButton);

      await waitFor(() => {
        expect(mockCreateSession).toHaveBeenCalledWith('task-1', 'Development Work');
      });
    });

    it('should show error when session creation fails', async () => {
      const errorMessage = 'Failed to create session';
      mockCreateSession.mockRejectedValue(new Error(errorMessage));

      render(<SessionDashboard />);
      
      const taskInput = screen.getByPlaceholderText('Select or enter task name...');
      fireEvent.change(taskInput, { target: { value: 'Development Work' } });
      
      const startButton = screen.getByText('Start New Session');
      fireEvent.click(startButton);

      await waitFor(() => {
        expect(mockShowError).toHaveBeenCalledWith(expect.stringContaining(errorMessage));
      });
    });

    it('should disable start button when no task is selected', () => {
      render(<SessionDashboard />);
      
      const startButton = screen.getByText('Start New Session');
      expect(startButton).toBeDisabled();
    });
  });

  describe('Session Display', () => {
    const mockSessions: TaskSession[] = [
      {
        id: 'session-1',
        taskId: 'task-1',
        taskName: 'Development Work',
        timerInstances: [
          {
            id: 'instance-1',
            sessionId: 'session-1',
            startTime: new Date('2024-01-15T10:00:00Z'),
            endTime: new Date('2024-01-15T11:00:00Z'),
            duration: 3600000, // 1 hour
            isRunning: false,
            isPaused: false,
            createdAt: '2024-01-15T10:00:00Z',
            updatedAt: '2024-01-15T11:00:00Z',
          },
        ],
        totalDuration: 3600000,
        isActive: false,
        date: '2024-01-15',
        createdAt: '2024-01-15T10:00:00Z',
        updatedAt: '2024-01-15T11:00:00Z',
      },
      {
        id: 'session-2',
        taskId: 'task-2',
        taskName: 'Code Review',
        timerInstances: [],
        totalDuration: 0,
        isActive: true,
        date: '2024-01-15',
        createdAt: '2024-01-15T11:00:00Z',
        updatedAt: '2024-01-15T11:00:00Z',
      },
    ];

    it('should display list of sessions', () => {
      vi.mocked(require('../../../hooks/useSessionManagement').useSessionManagement).mockReturnValue({
        sessions: mockSessions,
        activeSession: mockSessions[1],
        isLoading: false,
        error: null,
        createSession: mockCreateSession,
        updateSession: mockUpdateSession,
        deleteSession: mockDeleteSession,
        createTimerInstance: mockCreateTimerInstance,
        startTimer: mockStartTimer,
        stopTimer: mockStopTimer,
        pauseTimer: mockPauseTimer,
        resumeTimer: mockResumeTimer,
        refreshSessions: vi.fn(),
      });

      render(<SessionDashboard />);
      
      expect(screen.getByText('Development Work')).toBeInTheDocument();
      expect(screen.getByText('Code Review')).toBeInTheDocument();
      expect(screen.getByText('1:00:00')).toBeInTheDocument(); // 1 hour duration
    });

    it('should highlight active session', () => {
      vi.mocked(require('../../../hooks/useSessionManagement').useSessionManagement).mockReturnValue({
        sessions: mockSessions,
        activeSession: mockSessions[1],
        isLoading: false,
        error: null,
        createSession: mockCreateSession,
        updateSession: mockUpdateSession,
        deleteSession: mockDeleteSession,
        createTimerInstance: mockCreateTimerInstance,
        startTimer: mockStartTimer,
        stopTimer: mockStopTimer,
        pauseTimer: mockPauseTimer,
        resumeTimer: mockResumeTimer,
        refreshSessions: vi.fn(),
      });

      render(<SessionDashboard />);
      
      const activeSessionCard = screen.getByText('Code Review').closest('[data-testid="session-card"]');
      expect(activeSessionCard).toHaveClass('active');
    });
  });

  describe('Timer Management', () => {
    const mockActiveSession: TaskSession = {
      id: 'session-1',
      taskId: 'task-1',
      taskName: 'Development Work',
      timerInstances: [
        {
          id: 'timer-1',
          sessionId: 'session-1',
          startTime: new Date('2024-01-15T10:00:00Z'),
          endTime: null,
          duration: 0,
          isRunning: false,
          isPaused: false,
          createdAt: new Date('2024-01-15T10:00:00Z'),
          updatedAt: new Date('2024-01-15T10:00:00Z'),
        },
      ],
      startTime: new Date('2024-01-15T10:00:00Z'),
      endTime: null,
      totalDuration: 0,
      isActive: true,
      createdAt: new Date('2024-01-15T10:00:00Z'),
      updatedAt: new Date('2024-01-15T10:00:00Z'),
    };

    beforeEach(() => {
      vi.mocked(require('../../../hooks/useSessionManagement').useSessionManagement).mockReturnValue({
        sessions: [mockActiveSession],
        activeSession: mockActiveSession,
        isLoading: false,
        error: null,
        createSession: mockCreateSession,
        updateSession: mockUpdateSession,
        deleteSession: mockDeleteSession,
        createTimerInstance: mockCreateTimerInstance,
        startTimerInstance: mockStartTimer,
        stopTimerInstance: mockStopTimer,
        pauseTimerInstance: mockPauseTimer,
        resumeTimerInstance: mockResumeTimer,
        setActiveSession: vi.fn(),
        refreshSessions: vi.fn(),
      });
    });

    it('should create and start a timer instance when Add Timer is clicked', async () => {
      const newTimerInstance: TimerInstance = {
        id: 'timer-2',
        sessionId: 'session-1',
        startTime: new Date(),
        endTime: null,
        duration: 0,
        isRunning: false,
        isPaused: false,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockCreateTimerInstance.mockResolvedValue(newTimerInstance);
      mockStartTimer.mockResolvedValue(undefined);

      render(<SessionDashboard />);

      // Find and click the Add Timer button in SessionTimerBar
      const addTimerButton = screen.getByText('Add Timer');
      fireEvent.click(addTimerButton);

      await waitFor(() => {
        expect(mockCreateTimerInstance).toHaveBeenCalledWith('session-1');
        expect(mockStartTimer).toHaveBeenCalledWith('timer-2');
      });
    });

    it('should handle timer start operation', async () => {
      mockStartTimer.mockResolvedValue(undefined);

      render(<SessionDashboard />);

      // Find and click start timer button
      const startButton = screen.getByText('Start Timer');
      fireEvent.click(startButton);

      await waitFor(() => {
        expect(mockStartTimer).toHaveBeenCalledWith('timer-1');
      });
    });

    it('should handle timer stop operation', async () => {
      mockStopTimer.mockResolvedValue(undefined);

      render(<SessionDashboard />);

      // Find and click stop timer button
      const stopButton = screen.getByText('Stop Timer');
      fireEvent.click(stopButton);

      await waitFor(() => {
        expect(mockStopTimer).toHaveBeenCalledWith('timer-1');
      });
    });

    it('should handle timer pause operation', async () => {
      mockPauseTimer.mockResolvedValue(undefined);

      render(<SessionDashboard />);

      // Find and click pause timer button
      const pauseButton = screen.getByText('Pause Timer');
      fireEvent.click(pauseButton);

      await waitFor(() => {
        expect(mockPauseTimer).toHaveBeenCalledWith('timer-1');
      });
    });

    it('should handle timer resume operation', async () => {
      mockResumeTimer.mockResolvedValue(undefined);

      render(<SessionDashboard />);

      // Find and click resume timer button
      const resumeButton = screen.getByText('Resume Timer');
      fireEvent.click(resumeButton);

      await waitFor(() => {
        expect(mockResumeTimer).toHaveBeenCalledWith('timer-1');
      });
    });

    it('should show error when timer operations fail', async () => {
      mockStartTimer.mockRejectedValue(new Error('Timer start failed'));

      render(<SessionDashboard />);

      const startButton = screen.getByText('Start Timer');
      fireEvent.click(startButton);

      await waitFor(() => {
        expect(mockShowError).toHaveBeenCalledWith('Failed to start timer');
      });
    });
  });

  describe('Navigation and UI Interactions', () => {
    const mockSessions: TaskSession[] = [
      {
        id: 'session-1',
        taskId: 'task-1',
        taskName: 'Development Work',
        timerInstances: [],
        startTime: new Date('2024-01-15T10:00:00Z'),
        endTime: null,
        totalDuration: 3600000, // 1 hour
        isActive: false,
        createdAt: new Date('2024-01-15T10:00:00Z'),
        updatedAt: new Date('2024-01-15T10:00:00Z'),
      },
      {
        id: 'session-2',
        taskId: 'task-2',
        taskName: 'Code Review',
        timerInstances: [],
        startTime: new Date('2024-01-15T11:00:00Z'),
        endTime: null,
        totalDuration: 1800000, // 30 minutes
        isActive: true,
        createdAt: new Date('2024-01-15T11:00:00Z'),
        updatedAt: new Date('2024-01-15T11:00:00Z'),
      },
    ];

    beforeEach(() => {
      vi.mocked(require('../../../hooks/useSessionManagement').useSessionManagement).mockReturnValue({
        sessions: mockSessions,
        activeSession: mockSessions[1],
        isLoading: false,
        error: null,
        createSession: mockCreateSession,
        updateSession: mockUpdateSession,
        deleteSession: mockDeleteSession,
        createTimerInstance: mockCreateTimerInstance,
        startTimerInstance: mockStartTimer,
        stopTimerInstance: mockStopTimer,
        pauseTimerInstance: mockPauseTimer,
        resumeTimerInstance: mockResumeTimer,
        setActiveSession: vi.fn(),
        refreshSessions: vi.fn(),
      });
    });

    it('should handle session selection from session list', async () => {
      render(<SessionDashboard />);

      // Find and click on a session in the list
      const sessionCard = screen.getByText('Development Work').closest('[data-testid="session-card"]');
      expect(sessionCard).toBeInTheDocument();

      fireEvent.click(sessionCard!);

      // Verify session selection behavior (this would typically update some state)
      // Since we're mocking, we can't test the actual state change, but we can verify the UI responds
      expect(sessionCard).toBeInTheDocument();
    });

    it('should show floating action button for quick session creation', () => {
      render(<SessionDashboard />);

      const fab = screen.getByLabelText('create session');
      expect(fab).toBeInTheDocument();
    });

    it('should handle quick session creation from FAB', async () => {
      render(<SessionDashboard />);

      const fab = screen.getByLabelText('create session');
      fireEvent.click(fab);

      // Since the FAB currently shows an error message, verify that
      await waitFor(() => {
        expect(mockShowError).toHaveBeenCalledWith('Quick session creation not yet implemented');
      });
    });
  });

  describe('Inactivity Detection', () => {
    beforeEach(() => {
      vi.mocked(require('../../../hooks/useInactivityDetection').useInactivityDetection).mockReturnValue({
        isWarningShown: true,
        warningTimeRemaining: 30,
      });

      vi.mocked(require('../../../hooks/useSessionManagement').useSessionManagement).mockReturnValue({
        sessions: [],
        activeSession: {
          id: 'session-1',
          taskId: 'task-1',
          taskName: 'Development Work',
          timerInstances: [],
          startTime: new Date(),
          endTime: null,
          totalDuration: 0,
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        isLoading: false,
        error: null,
        createSession: mockCreateSession,
        updateSession: mockUpdateSession,
        deleteSession: mockDeleteSession,
        createTimerInstance: mockCreateTimerInstance,
        startTimerInstance: mockStartTimer,
        stopTimerInstance: mockStopTimer,
        pauseTimerInstance: mockPauseTimer,
        resumeTimerInstance: mockResumeTimer,
        setActiveSession: vi.fn(),
        refreshSessions: vi.fn(),
      });
    });

    it('should show inactivity warning dialog when warning is active', () => {
      render(<SessionDashboard />);

      expect(screen.getByText('Inactivity Warning')).toBeInTheDocument();
      expect(screen.getByText('Continue Working')).toBeInTheDocument();
      expect(screen.getByText('Pause Timer')).toBeInTheDocument();
    });

    it('should handle continue work action from inactivity warning', async () => {
      const dispatchEventSpy = vi.spyOn(window, 'dispatchEvent');

      render(<SessionDashboard />);

      const continueButton = screen.getByText('Continue Working');
      fireEvent.click(continueButton);

      expect(dispatchEventSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'timer-continue-activity',
          detail: expect.objectContaining({
            reason: 'user-action',
          }),
        })
      );
    });

    it('should handle pause from inactivity action', async () => {
      const dispatchEventSpy = vi.spyOn(window, 'dispatchEvent');

      render(<SessionDashboard />);

      const pauseButton = screen.getByText('Pause Timer');
      fireEvent.click(pauseButton);

      expect(dispatchEventSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'timer-pause-inactivity',
          detail: expect.objectContaining({
            reason: 'user-action',
          }),
        })
      );
    });
  });

  describe('Notes Integration', () => {
    const mockActiveSession: TaskSession = {
      id: 'session-1',
      taskId: 'task-1',
      taskName: 'Development Work',
      timerInstances: [
        {
          id: 'timer-1',
          sessionId: 'session-1',
          startTime: new Date(),
          endTime: null,
          duration: 0,
          isRunning: false,
          isPaused: false,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ],
      startTime: new Date(),
      endTime: null,
      totalDuration: 0,
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    beforeEach(() => {
      vi.mocked(require('../../../hooks/useSessionManagement').useSessionManagement).mockReturnValue({
        sessions: [mockActiveSession],
        activeSession: mockActiveSession,
        isLoading: false,
        error: null,
        createSession: mockCreateSession,
        updateSession: mockUpdateSession,
        deleteSession: mockDeleteSession,
        createTimerInstance: mockCreateTimerInstance,
        startTimerInstance: mockStartTimer,
        stopTimerInstance: mockStopTimer,
        pauseTimerInstance: mockPauseTimer,
        resumeTimerInstance: mockResumeTimer,
        setActiveSession: vi.fn(),
        refreshSessions: vi.fn(),
      });
    });

    it('should open notes dialog when notes button is clicked', async () => {
      render(<SessionDashboard />);

      // Find and click notes button
      const notesButton = screen.getByText('Notes');
      fireEvent.click(notesButton);

      // Verify notes dialog is opened
      expect(screen.getByTestId('notes-dialog')).toBeInTheDocument();
    });

    it('should close notes dialog when close button is clicked', async () => {
      render(<SessionDashboard />);

      // Open notes dialog first
      const notesButton = screen.getByText('Notes');
      fireEvent.click(notesButton);

      expect(screen.getByTestId('notes-dialog')).toBeInTheDocument();

      // Close the dialog
      const closeButton = screen.getByText('Close');
      fireEvent.click(closeButton);

      // Verify dialog is closed
      expect(screen.queryByTestId('notes-dialog')).not.toBeInTheDocument();
    });
  });
});
