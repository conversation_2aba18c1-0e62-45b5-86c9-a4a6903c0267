/**
 * Timer Synchronization Hook
 *
 * Bridges the gap between the global timer system and session timer system
 * to ensure they stay synchronized.
 */

import { useEffect, useCallback } from 'react';
import { TimeEntry, TaskSession, TimerInstance } from '../types/timer';
import { safeInvoke, safeListen, isTauriEnvironment } from '../utils/tauri';

interface TimerSyncProps {
  activeEntry: TimeEntry | null;
  setActiveEntry: (entry: TimeEntry | null) => void;
  activeSession: TaskSession | null;
}

export function useTimerSync({ activeEntry, setActiveEntry }: TimerSyncProps) {
  // Safe error handling without NotificationProvider dependency
  const showError = useCallback((message: string) => {
    console.error('Timer Sync Error:', message);
  }, []);

  const showWarning = useCallback((message: string) => {
    console.warn('Timer Sync Warning:', message);
  }, []);

  // Conflict resolution: Check if both global and session timers are running
  const detectAndResolveConflicts = useCallback(async () => {
    if (!isTauriEnvironment()) return;

    try {
      // Get current session state
      const sessions = await safeInvoke<TaskSession[]>('get_sessions');
      if (!sessions) return;

      const runningSessions = sessions.filter(s =>
        s.timerInstances?.some(i => i.isRunning)
      );

      // Check for conflicts
      if (activeEntry && !activeEntry.id.startsWith('session_') && runningSessions.length > 0) {
        // Both global timer and session timer are running - this is a conflict
        console.warn('Timer conflict detected: Both global and session timers are running');

        showWarning(
          'Timer conflict detected. Session timer will take priority. Global timer has been paused.'
        );

        // Pause the global timer in favor of session timer
        const pausedGlobalEntry: TimeEntry = {
          ...activeEntry,
          isRunning: false,
        };
        setActiveEntry(pausedGlobalEntry);
      }
    } catch (error) {
      console.warn('Failed to detect timer conflicts:', error);
    }
  }, [activeEntry, setActiveEntry, showError, showWarning]);

  // Listen for session timer events and sync with global timer
  useEffect(() => {
    if (!isTauriEnvironment()) {
      console.log('Skipping Tauri event listeners in web environment');
      return;
    }

    let unsubscribeStart: (() => void) | null = null;
    let unsubscribeStop: (() => void) | null = null;
    let unsubscribePause: (() => void) | null = null;
    let unsubscribeResume: (() => void) | null = null;

    const setupListeners = async () => {
      try {
        unsubscribeStart = await safeListen('timer-instance-started', async (event: any) => {
          try {
            console.log('Timer instance started event received:', event.payload);

            // Get the session that contains this timer instance
            const sessions = await safeInvoke<TaskSession[]>('get_sessions');
            if (!sessions) return;

            const session = sessions.find(s =>
              s.timerInstances?.some((i: TimerInstance) => i.id === event.payload.instanceId)
            );

            if (session) {
              const instance = session.timerInstances?.find((i: TimerInstance) => i.id === event.payload.instanceId);
              if (instance) {
                console.log('Creating global timer entry for session timer:', {
                  sessionId: session.id,
                  instanceId: instance.id,
                  taskName: session.taskName
                });

                // Create a TimeEntry that represents the session timer
                const sessionTimeEntry: TimeEntry = {
                  id: `session_${instance.id}`,
                  taskName: session.taskName,
                  taskId: session.taskId,
                  startTime: new Date(event.payload.startTime),
                  isRunning: true,
                  date: new Date().toISOString().split('T')[0],
                };

                setActiveEntry(sessionTimeEntry);

                // Check for conflicts after setting session timer
                await detectAndResolveConflicts();
              }
            } else {
              console.warn('Session not found for timer instance:', event.payload.instanceId);
            }
          } catch (error) {
            console.warn('Failed to sync session timer start with global timer:', error);
          }
        });

        unsubscribeStop = await safeListen('timer-instance-stopped', async (event: any) => {
          try {
            // Clear the global timer when session timer stops
            if (activeEntry && activeEntry.id.startsWith('session_')) {
              const instanceId = activeEntry.id.replace('session_', '');
              // Check if the stopped instance matches the current active entry
              if (event.payload.instanceId === instanceId) {
                console.log('Clearing global timer for stopped session timer:', instanceId);
                setActiveEntry(null);
              }
            }
          } catch (error) {
            console.warn('Failed to sync session timer stop with global timer:', error);
          }
        });

        unsubscribePause = await safeListen('timer-instance-paused', async (event: any) => {
          try {
            // Pause the global timer when session timer pauses
            if (activeEntry && activeEntry.id.startsWith('session_')) {
              const instanceId = activeEntry.id.replace('session_', '');
              // Check if the paused instance matches the current active entry
              if (event.payload.instanceId === instanceId) {
                console.log('Pausing global timer for paused session timer:', instanceId);
                const pausedEntry: TimeEntry = {
                  ...activeEntry,
                  isRunning: false,
                };
                setActiveEntry(pausedEntry);
              }
            }
          } catch (error) {
            console.warn('Failed to sync session timer pause with global timer:', error);
          }
        });

        unsubscribeResume = await safeListen('timer-instance-resumed', async (event: any) => {
          try {
            // Resume the global timer when session timer resumes
            if (activeEntry && activeEntry.id.startsWith('session_')) {
              const instanceId = activeEntry.id.replace('session_', '');
              // Check if the resumed instance matches the current active entry
              if (event.payload.instanceId === instanceId) {
                console.log('Resuming global timer for resumed session timer:', instanceId);
                const resumedEntry: TimeEntry = {
                  ...activeEntry,
                  isRunning: true,
                  startTime: new Date(event.payload.resumedAt), // Use resume time as new start time
                };
                setActiveEntry(resumedEntry);
              }
            }
          } catch (error) {
            console.warn('Failed to sync session timer resume with global timer:', error);
          }
        });
      } catch (error) {
        console.warn('Failed to setup Tauri event listeners:', error);
      }
    };

    setupListeners();

    return () => {
      if (unsubscribeStart) unsubscribeStart();
      if (unsubscribeStop) unsubscribeStop();
      if (unsubscribePause) unsubscribePause();
      if (unsubscribeResume) unsubscribeResume();
    };
  }, [activeEntry, setActiveEntry]);

  // Sync global timer changes with session system
  const syncGlobalTimerWithSessions = useCallback(async (entry: TimeEntry | null) => {
    if (!isTauriEnvironment()) {
      console.log('Skipping Tauri timer state sync in web environment');
      return;
    }

    try {
      if (entry && !entry.id.startsWith('session_')) {
        // This is a global timer, update the backend timer state
        await safeInvoke('update_timer_state', {
          isRunning: entry.isRunning,
          taskName: entry.taskName,
          startTime: entry.startTime instanceof Date
            ? entry.startTime.toISOString()
            : entry.startTime,
          elapsedMs: entry.isRunning ? Date.now() - new Date(entry.startTime).getTime() : 0,
        });
      } else if (!entry) {
        // No active timer, clear backend state
        await safeInvoke('update_timer_state', {
          isRunning: false,
          taskName: '',
          startTime: null,
          elapsedMs: 0,
        });
      }
    } catch (error) {
      console.warn('Failed to sync global timer with backend:', error);
    }
  }, []);

  return {
    syncGlobalTimerWithSessions,
    detectAndResolveConflicts,
  };
}
