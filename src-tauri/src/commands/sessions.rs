// Session-related command handlers

use crate::state::AppState;
use crate::types::{TaskSession, TimerInstance, EnhancedTimerState, InactivitySettings, TimerActivityState};
use chrono::Utc;
use tauri::{AppHandle, State, Emitter};

#[tauri::command]
pub fn create_session(
    app_state: State<AppState>,
    task_id: String,
    task_name: String,
) -> Result<TaskSession, String> {
    let now = Utc::now();
    let session_id = format!("session_{}_{}", now.timestamp_millis(), now.timestamp_micros() % 1000000);

    let new_session = TaskSession {
        id: session_id,
        task_id,
        task_name,
        timer_instances: Vec::new(),
        total_duration: 0,
        notes: None,
        is_active: false,
        date: now.format("%Y-%m-%d").to_string(),
        created_at: now.to_rfc3339(),
        updated_at: now.to_rfc3339(),
    };

    app_state.with_task_sessions_mut(|sessions| {
        sessions.push(new_session.clone());
    })?;

    Ok(new_session)
}

#[tauri::command]
pub fn create_session_with_timer(
    app: AppHandle,
    app_state: State<AppState>,
    task_id: String,
    task_name: String,
) -> Result<(TaskSession, TimerInstance), String> {
    let now = Utc::now();
    let session_id = format!("session_{}_{}", now.timestamp_millis(), now.timestamp_micros() % 1000000);
    let instance_id = format!("instance_{}_{}", now.timestamp_millis(), now.timestamp_micros() % 1000000);

    // Create the timer instance
    let new_instance = TimerInstance {
        id: instance_id.clone(),
        session_id: session_id.clone(),
        start_time: now,
        end_time: None,
        duration: None,
        is_running: true,  // Start the timer immediately
        is_paused: false,
        paused_at: None,
        paused_duration: None,
        notes: None,
        created_at: now.to_rfc3339(),
        updated_at: now.to_rfc3339(),
    };

    // Create the session with the timer instance
    let new_session = TaskSession {
        id: session_id.clone(),
        task_id,
        task_name: task_name.clone(),
        timer_instances: vec![new_instance.clone()],
        total_duration: 0,
        notes: None,
        is_active: true,  // Make the session active immediately
        date: now.format("%Y-%m-%d").to_string(),
        created_at: now.to_rfc3339(),
        updated_at: now.to_rfc3339(),
    };

    // Add session to state
    app_state.with_task_sessions_mut(|sessions| {
        sessions.push(new_session.clone());
    })?;

    // Update enhanced timer state
    app_state.with_enhanced_timer_state_mut(|enhanced_state| {
        enhanced_state.is_running = true;
        enhanced_state.task_name = task_name.clone();
        enhanced_state.session_id = Some(session_id.clone());
        enhanced_state.instance_id = Some(instance_id.clone());
        enhanced_state.start_time = Some(now);
        enhanced_state.elapsed_ms = 0;
        enhanced_state.is_paused = false;
        enhanced_state.paused_due_to_inactivity = false;
    })?;

    // Emit event to frontend
    app.emit("timer-instance-started", serde_json::json!({
        "instanceId": instance_id,
        "sessionId": session_id,
        "startTime": now.to_rfc3339()
    })).map_err(|e| format!("Failed to emit timer instance started event: {}", e))?;

    Ok((new_session, new_instance))
}


#[tauri::command]
pub fn get_sessions(app_state: State<AppState>) -> Result<Vec<TaskSession>, String> {
    app_state.with_task_sessions(|sessions| sessions.clone())
}

#[tauri::command]
pub fn get_session_by_id(
    app_state: State<AppState>,
    session_id: String,
) -> Result<Option<TaskSession>, String> {
    app_state.with_task_sessions(|sessions| {
        sessions.iter().find(|s| s.id == session_id).cloned()
    })
}

#[tauri::command]
pub fn update_session(
    app_state: State<AppState>,
    session_id: String,
    updates: serde_json::Value,
) -> Result<TaskSession, String> {
    let now = Utc::now();
    
    app_state.with_task_sessions_mut(|sessions| {
        let session_index = sessions
            .iter()
            .position(|s| s.id == session_id)
            .ok_or_else(|| format!("Session with ID {} not found", session_id))?;

        let session = &mut sessions[session_index];
        
        // Update fields based on the updates object
        if let Some(task_name) = updates.get("task_name").and_then(|v| v.as_str()) {
            session.task_name = task_name.to_string();
        }
        if let Some(notes) = updates.get("notes").and_then(|v| v.as_str()) {
            session.notes = Some(notes.to_string());
        }
        if let Some(is_active) = updates.get("is_active").and_then(|v| v.as_bool()) {
            session.is_active = is_active;
        }
        
        session.updated_at = now.to_rfc3339();
        
        Ok(session.clone())
    })?
}

#[tauri::command]
pub fn delete_session(
    app_state: State<AppState>,
    session_id: String,
) -> Result<(), String> {
    app_state.with_task_sessions_mut(|sessions| {
        let initial_len = sessions.len();
        sessions.retain(|s| s.id != session_id);
        
        if sessions.len() == initial_len {
            return Err(format!("Session with ID {} not found", session_id));
        }
        
        Ok(())
    })?
}

#[tauri::command]
pub fn create_timer_instance(
    app_state: State<AppState>,
    session_id: String,
) -> Result<TimerInstance, String> {
    let now = Utc::now();
    let instance_id = format!("instance_{}_{}", now.timestamp_millis(), now.timestamp_micros() % 1000000);

    let new_instance = TimerInstance {
        id: instance_id,
        session_id: session_id.clone(),
        start_time: now,
        end_time: None,
        duration: None,
        is_running: false,
        is_paused: false,
        paused_at: None,
        paused_duration: None,
        notes: None,
        created_at: now.to_rfc3339(),
        updated_at: now.to_rfc3339(),
    };

    // Add instance to the session
    app_state.with_task_sessions_mut(|sessions| -> Result<(), String> {
        let session = sessions
            .iter_mut()
            .find(|s| s.id == session_id)
            .ok_or_else(|| format!("Session with ID {} not found", session_id))?;

        session.timer_instances.push(new_instance.clone());
        session.updated_at = now.to_rfc3339();

        Ok(())
    })??;

    Ok(new_instance)
}

#[tauri::command]
pub fn update_timer_instance(
    app_state: State<AppState>,
    instance_id: String,
    updates: serde_json::Value,
) -> Result<TimerInstance, String> {
    let now = Utc::now();
    
    app_state.with_task_sessions_mut(|sessions| {
        for session in sessions.iter_mut() {
            if let Some(instance) = session.timer_instances.iter_mut().find(|i| i.id == instance_id) {
                // Update fields based on the updates object
                if let Some(is_running) = updates.get("is_running").and_then(|v| v.as_bool()) {
                    instance.is_running = is_running;
                }
                if let Some(is_paused) = updates.get("is_paused").and_then(|v| v.as_bool()) {
                    instance.is_paused = is_paused;
                }
                if let Some(notes) = updates.get("notes").and_then(|v| v.as_str()) {
                    instance.notes = Some(notes.to_string());
                }
                if let Some(duration) = updates.get("duration").and_then(|v| v.as_u64()) {
                    instance.duration = Some(duration);
                }

                instance.updated_at = now.to_rfc3339();
                session.updated_at = now.to_rfc3339();

                let result = instance.clone();

                // Recalculate session total duration after we're done with the mutable borrow
                session.total_duration = session.timer_instances
                    .iter()
                    .filter_map(|i| i.duration)
                    .sum();

                return Ok(result);
            }
        }
        
        Err(format!("Timer instance with ID {} not found", instance_id))
    })?
}

#[tauri::command]
pub fn start_timer_instance(
    app: AppHandle,
    app_state: State<AppState>,
    instance_id: String,
) -> Result<(), String> {
    let now = Utc::now();
    
    // Update the timer instance
    app_state.with_task_sessions_mut(|sessions| {
        for session in sessions.iter_mut() {
            if let Some(instance) = session.timer_instances.iter_mut().find(|i| i.id == instance_id) {
                instance.is_running = true;
                instance.is_paused = false;
                instance.start_time = now;
                instance.updated_at = now.to_rfc3339();
                session.updated_at = now.to_rfc3339();
                
                // Update enhanced timer state
                app_state.with_enhanced_timer_state_mut(|enhanced_state| {
                    enhanced_state.is_running = true;
                    enhanced_state.task_name = session.task_name.clone();
                    enhanced_state.session_id = Some(session.id.clone());
                    enhanced_state.instance_id = Some(instance_id.clone());
                    enhanced_state.start_time = Some(now);
                    enhanced_state.elapsed_ms = 0;
                    enhanced_state.is_paused = false;
                    enhanced_state.paused_due_to_inactivity = false;
                })?;
                
                return Ok(());
            }
        }
        
        Err(format!("Timer instance with ID {} not found", instance_id))
    })??;

    // Emit event to frontend
    app.emit("timer-instance-started", serde_json::json!({
        "instanceId": instance_id,
        "startTime": now.to_rfc3339()
    })).map_err(|e| format!("Failed to emit timer instance started event: {}", e))?;

    Ok(())
}

#[tauri::command]
pub fn stop_timer_instance(
    app: AppHandle,
    app_state: State<AppState>,
    instance_id: String,
) -> Result<(), String> {
    let now = Utc::now();
    
    // Update the timer instance
    app_state.with_task_sessions_mut(|sessions| {
        for session in sessions.iter_mut() {
            if let Some(instance) = session.timer_instances.iter_mut().find(|i| i.id == instance_id) {
                instance.is_running = false;
                instance.is_paused = false;
                instance.end_time = Some(now);

                // Calculate duration, accounting for current pause state
                let total_elapsed = now.timestamp_millis() - instance.start_time.timestamp_millis();
                let mut total_paused_duration = instance.paused_duration.unwrap_or(0) as i64;

                // If currently paused, add the current pause duration
                if instance.is_paused {
                    if let Some(paused_at) = instance.paused_at {
                        let current_pause_duration = now.timestamp_millis() - paused_at.timestamp_millis();
                        total_paused_duration += current_pause_duration;
                    }
                }

                instance.duration = Some((total_elapsed - total_paused_duration).max(0) as u64);

                instance.updated_at = now.to_rfc3339();
                session.updated_at = now.to_rfc3339();

                // Store the duration value before ending the mutable borrow
                let final_duration = instance.duration.unwrap_or(0);

                // Recalculate session total duration
                session.total_duration = session.timer_instances
                    .iter()
                    .filter_map(|i| i.duration)
                    .sum();

                // Update enhanced timer state
                app_state.with_enhanced_timer_state_mut(|enhanced_state| {
                    enhanced_state.is_running = false;
                    enhanced_state.is_paused = false;
                    enhanced_state.paused_due_to_inactivity = false;
                    enhanced_state.elapsed_ms = final_duration;
                })?;

                return Ok(());
            }
        }
        
        Err(format!("Timer instance with ID {} not found", instance_id))
    })??;

    // Emit event to frontend
    app.emit("timer-instance-stopped", serde_json::json!({
        "instanceId": instance_id,
        "endTime": now.to_rfc3339()
    })).map_err(|e| format!("Failed to emit timer instance stopped event: {}", e))?;

    Ok(())
}

#[tauri::command]
pub fn get_enhanced_timer_state(app_state: State<AppState>) -> Result<EnhancedTimerState, String> {
    app_state.with_enhanced_timer_state(|state| state.clone())
}

#[tauri::command]
pub fn update_inactivity_settings(
    app_state: State<AppState>,
    settings: InactivitySettings,
) -> Result<(), String> {
    app_state.with_inactivity_settings_mut(|current_settings| {
        *current_settings = settings;
    })?;
    
    Ok(())
}

#[tauri::command]
pub fn get_inactivity_settings(app_state: State<AppState>) -> Result<InactivitySettings, String> {
    app_state.with_inactivity_settings(|settings| settings.clone())
}

#[tauri::command]
pub fn update_timer_activity_state(
    app_state: State<AppState>,
    activity_state: TimerActivityState,
) -> Result<(), String> {
    app_state.with_timer_activity_state_mut(|current_state| {
        *current_state = activity_state;
    })?;
    
    Ok(())
}

#[tauri::command]
pub fn get_timer_activity_state(app_state: State<AppState>) -> Result<TimerActivityState, String> {
    app_state.with_timer_activity_state(|state| state.clone())
}

#[tauri::command]
pub fn pause_timer_instance(
    app: AppHandle,
    app_state: State<AppState>,
    instance_id: String,
) -> Result<(), String> {
    let now = Utc::now();

    // Update the timer instance to paused state
    app_state.with_task_sessions_mut(|sessions| {
        for session in sessions.iter_mut() {
            if let Some(instance) = session.timer_instances.iter_mut().find(|i| i.id == instance_id) {
                // Can only pause a running timer
                if !instance.is_running {
                    return Err("Cannot pause timer: timer is not running".to_string());
                }

                // Update instance state
                instance.is_running = false;
                instance.is_paused = true;
                instance.paused_at = Some(now);
                instance.updated_at = now.to_rfc3339();
                session.updated_at = now.to_rfc3339();

                // Update enhanced timer state
                app_state.with_enhanced_timer_state_mut(|enhanced_state| {
                    enhanced_state.is_running = false;
                    enhanced_state.is_paused = true;
                    // Calculate elapsed time up to pause point
                    if let Some(start_time) = enhanced_state.start_time {
                        enhanced_state.elapsed_ms = (now.timestamp_millis() - start_time.timestamp_millis()) as u64;
                    }
                })?;

                return Ok(());
            }
        }

        Err(format!("Timer instance with ID {} not found", instance_id))
    })??;

    // Emit event to frontend
    app.emit("timer-instance-paused", serde_json::json!({
        "instanceId": instance_id,
        "pausedAt": now.to_rfc3339()
    })).map_err(|e| format!("Failed to emit timer instance paused event: {}", e))?;

    Ok(())
}

#[tauri::command]
pub fn resume_timer_instance(
    app: AppHandle,
    app_state: State<AppState>,
    instance_id: String,
) -> Result<(), String> {
    let now = Utc::now();

    // Update the timer instance to resume from paused state
    app_state.with_task_sessions_mut(|sessions| {
        for session in sessions.iter_mut() {
            if let Some(instance) = session.timer_instances.iter_mut().find(|i| i.id == instance_id) {
                // Can only resume a paused timer
                if !instance.is_paused {
                    return Err("Cannot resume timer: timer is not paused".to_string());
                }

                // Calculate paused duration and add to total
                if let Some(paused_at) = instance.paused_at {
                    let pause_duration = (now.timestamp_millis() - paused_at.timestamp_millis()) as u64;
                    instance.paused_duration = Some(
                        instance.paused_duration.unwrap_or(0) + pause_duration
                    );
                }

                // Update instance state
                instance.is_running = true;
                instance.is_paused = false;
                instance.paused_at = None;
                instance.updated_at = now.to_rfc3339();
                session.updated_at = now.to_rfc3339();

                // Update enhanced timer state
                app_state.with_enhanced_timer_state_mut(|enhanced_state| {
                    enhanced_state.is_running = true;
                    enhanced_state.is_paused = false;
                    enhanced_state.start_time = Some(now); // Reset start time for resumed timer
                })?;

                return Ok(());
            }
        }

        Err(format!("Timer instance with ID {} not found", instance_id))
    })??;

    // Emit event to frontend
    app.emit("timer-instance-resumed", serde_json::json!({
        "instanceId": instance_id,
        "resumedAt": now.to_rfc3339()
    })).map_err(|e| format!("Failed to emit timer instance resumed event: {}", e))?;

    Ok(())
}